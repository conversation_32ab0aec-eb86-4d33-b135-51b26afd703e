'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import {
  Heart,
  Calendar,
  Users,
  FileText,
  DollarSign,
  ArrowRight,
  CheckCircle,
  Stethoscope,
  Shield,
  Sparkles,
  TrendingUp,
  Clock,
  Award,
  PawPrint,
  Brain,
  Database,
  CreditCard,
  UserCheck,
  Activity,
  Lock,
  Smartphone,
  BarChart3,
  Layers,
  Settings,
  Camera,
  Zap,
  Bell,
  Globe,
  Star
} from 'lucide-react';

export default function Home() {
  return (
    <div className="min-h-screen overflow-hidden">
      {/* Simplified Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-cyan-50" />
        <div className="absolute inset-0 bg-gradient-to-tr from-purple-50/30 via-transparent to-pink-50/30" />

        {/* Simplified Static Elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-blue-200/20 to-purple-200/20 rounded-full blur-xl" />
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-green-200/20 to-blue-200/20 rounded-full blur-xl" />
        <div className="absolute bottom-20 left-1/3 w-40 h-40 bg-gradient-to-br from-pink-200/20 to-indigo-200/20 rounded-full blur-xl" />
      </div>

      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-white/70 backdrop-blur-xl border-b border-white/20 shadow-lg shadow-black/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <PawPrint className="h-10 w-10 text-blue-600" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full" />
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-green-500 bg-clip-text text-transparent">
                VetCare Pro
              </span>
            </div>

            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium">
                Features
              </a>
              <Link href="/api-test" className="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium">
                API Test
              </Link>
              <Link href="/dashboard" className="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium">
                Dashboard
              </Link>
              <Link href="/auth/login">
                <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300">
                  Get Started
                </button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative pt-32 pb-20 px-4 sm:px-6 lg:px-8 min-h-screen flex items-center">
        <div className="max-w-7xl mx-auto w-full relative z-10">
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              className="mb-8"
            >
              <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm px-6 py-3 rounded-full border border-white/20 shadow-lg">
                <Sparkles className="h-5 w-5 text-yellow-500" />
                <span className="text-sm font-medium text-gray-700">✨ Soul-Capturing Veterinary Management</span>
                <Sparkles className="h-5 w-5 text-yellow-500" />
              </div>
            </motion.div>

            <h1 className="text-6xl md:text-8xl lg:text-9xl font-bold mb-8 leading-tight">
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent block">
                Veterinary
              </span>
              <span className="bg-gradient-to-r from-green-500 via-blue-500 to-purple-600 bg-clip-text text-transparent block">
                Management
              </span>
              <span className="text-gray-800 block text-4xl md:text-5xl lg:text-6xl mt-4">
                That Captures <span className="italic text-transparent bg-gradient-to-r from-pink-500 to-rose-500 bg-clip-text">Souls</span>
              </span>
            </h1>

            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="text-xl md:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto leading-relaxed"
            >
              Transform your veterinary practice with our <span className="font-semibold text-blue-600">beautiful</span>,
              <span className="font-semibold text-purple-600"> intuitive platform</span>.
              Manage patients, staff, appointments, and billing with <span className="font-semibold text-pink-600">soul-capturing design</span>.
            </motion.p>

            {/* Key Features Preview */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto mb-12"
            >
              {[
                { icon: Users, label: "Multi-Role Management", color: "blue" },
                { icon: Brain, label: "AI-Powered Scheduling", color: "purple" },
                { icon: Database, label: "Digital Health Records", color: "green" },
                { icon: CreditCard, label: "Automated Billing", color: "orange" }
              ].map((feature, index) => (
                <motion.div
                  key={feature.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.8 + (index * 0.1) }}
                  className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300 group cursor-pointer"
                >
                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${
                    feature.color === 'blue' ? 'from-blue-500 to-blue-600' :
                    feature.color === 'purple' ? 'from-purple-500 to-purple-600' :
                    feature.color === 'green' ? 'from-green-500 to-green-600' :
                    'from-orange-500 to-orange-600'
                  } flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="h-6 w-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-gray-800 text-sm leading-tight">{feature.label}</h4>
                </motion.div>
              ))}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="flex flex-col sm:flex-row gap-6 justify-center items-center"
            >
              <Link href="/auth/login">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white px-10 py-5 rounded-2xl text-lg font-bold shadow-2xl hover:shadow-3xl transition-all duration-300 flex items-center gap-3 relative overflow-hidden group"
                >
                  <span className="relative z-10">Start Free Trial</span>
                  <ArrowRight className="h-6 w-6 relative z-10 group-hover:translate-x-1 transition-transform duration-300" />
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-pink-600 via-purple-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  />
                </motion.button>
              </Link>
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-gray-300 text-gray-700 px-10 py-5 rounded-2xl text-lg font-bold hover:border-blue-400 hover:text-blue-600 hover:bg-blue-50/50 transition-all duration-300 backdrop-blur-sm bg-white/70"
              >
                Watch Demo
              </motion.button>
            </motion.div>
          </div>

          {/* Hero Cards - Asymmetric Layout */}
          <motion.div
            initial={{ opacity: 0, y: 60 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 1.0 }}
            className="mt-20 relative"
          >
            {/* Main Dashboard Preview Card */}
            <div className="relative max-w-6xl mx-auto">
              <motion.div
                whileHover={{ y: -10, rotateY: 5 }}
                transition={{ duration: 0.3 }}
                className="bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-white/20 relative overflow-hidden"
              >
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-transparent to-purple-50/50 pointer-events-none" />

                <div className="relative z-10">
                  <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    </div>
                    <div className="text-sm text-gray-500 font-medium">VetCare Pro Dashboard</div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Card 1 - Asymmetric */}
                    <motion.div
                      whileHover={{ scale: 1.05, rotate: 1 }}
                      className="bg-gradient-to-br from-blue-500 to-blue-600 p-6 rounded-2xl text-white transform -rotate-1 hover:rotate-0 transition-all duration-300 shadow-lg"
                    >
                      <Calendar className="h-10 w-10 mb-4 opacity-90" />
                      <h3 className="font-bold mb-2 text-lg">Smart Scheduling</h3>
                      <p className="text-sm opacity-90 leading-relaxed">AI-powered appointment management with conflict detection</p>
                      <div className="mt-4 flex items-center gap-2">
                        <div className="w-2 h-2 bg-white/60 rounded-full"></div>
                        <div className="w-2 h-2 bg-white/40 rounded-full"></div>
                        <div className="w-2 h-2 bg-white/20 rounded-full"></div>
                      </div>
                    </motion.div>

                    {/* Card 2 - Symmetric */}
                    <motion.div
                      whileHover={{ scale: 1.05, y: -5 }}
                      className="bg-gradient-to-br from-green-500 to-emerald-600 p-6 rounded-2xl text-white shadow-lg"
                    >
                      <FileText className="h-10 w-10 mb-4 opacity-90" />
                      <h3 className="font-bold mb-2 text-lg">Digital Records</h3>
                      <p className="text-sm opacity-90 leading-relaxed">Comprehensive patient histories and medical data</p>
                      <div className="mt-4 bg-white/20 rounded-full h-2 overflow-hidden">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: "75%" }}
                          transition={{ delay: 1.5, duration: 1 }}
                          className="h-full bg-white/60 rounded-full"
                        />
                      </div>
                    </motion.div>

                    {/* Card 3 - Asymmetric */}
                    <motion.div
                      whileHover={{ scale: 1.05, rotate: -1 }}
                      className="bg-gradient-to-br from-purple-500 to-pink-600 p-6 rounded-2xl text-white transform rotate-1 hover:rotate-0 transition-all duration-300 shadow-lg"
                    >
                      <DollarSign className="h-10 w-10 mb-4 opacity-90" />
                      <h3 className="font-bold mb-2 text-lg">Automated Billing</h3>
                      <p className="text-sm opacity-90 leading-relaxed">Streamlined payment processing and invoicing</p>
                      <div className="mt-4 flex justify-between items-center">
                        <span className="text-xs opacity-75">Revenue</span>
                        <span className="text-lg font-bold">+24%</span>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </motion.div>

              {/* Floating Stats Cards */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.3, duration: 0.8 }}
                className="absolute -left-8 top-1/2 transform -translate-y-1/2 hidden lg:block"
              >
                <div className="bg-white/90 backdrop-blur-xl rounded-2xl p-4 shadow-xl border border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center">
                      <TrendingUp className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-gray-800">98%</div>
                      <div className="text-sm text-gray-600">Satisfaction</div>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.5, duration: 0.8 }}
                className="absolute -right-8 bottom-1/4 hidden lg:block"
              >
                <div className="bg-white/90 backdrop-blur-xl rounded-2xl p-4 shadow-xl border border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-xl flex items-center justify-center">
                      <Clock className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-gray-800">2.5x</div>
                      <div className="text-sm text-gray-600">Faster</div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>
      {/* Features Section */}
      <section id="features" className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white" />
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 50, repeat: Infinity, ease: "linear" }}
          className="absolute top-10 right-10 w-64 h-64 bg-gradient-to-br from-blue-100/30 to-purple-100/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{ rotate: -360 }}
          transition={{ duration: 60, repeat: Infinity, ease: "linear" }}
          className="absolute bottom-10 left-10 w-80 h-80 bg-gradient-to-br from-green-100/30 to-pink-100/30 rounded-full blur-3xl"
        />

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-20">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              className="mb-6"
            >
              <span className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-purple-100 px-6 py-3 rounded-full text-sm font-semibold text-gray-700 border border-blue-200/50">
                <Award className="h-4 w-4 text-blue-600" />
                Complete Veterinary Solution
              </span>
            </motion.div>

            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-5xl md:text-7xl font-bold mb-6"
            >
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">Everything</span>
              <br />
              <span className="text-gray-800">You Need</span>
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed"
            >
              Comprehensive veterinary management tools designed to
              <span className="font-semibold text-blue-600"> streamline your practice</span> and
              <span className="font-semibold text-purple-600"> delight your clients</span>
            </motion.p>
          </div>

          {/* Enhanced Features Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 max-w-7xl mx-auto">
            {/* Multi-Role Management - Large Feature Card */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="lg:col-span-8 bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 p-10 rounded-3xl text-white relative overflow-hidden group hover:scale-[1.02] transition-all duration-500"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <div className="relative z-10">
                <div className="flex items-start justify-between mb-8">
                  <div className="p-4 bg-white/20 rounded-2xl backdrop-blur-sm">
                    <Users className="h-12 w-12" />
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold">98%</div>
                    <div className="text-sm opacity-90">User Satisfaction</div>
                  </div>
                </div>
                <h3 className="text-3xl font-bold mb-4">Multi-Role Management</h3>
                <p className="text-lg opacity-90 leading-relaxed mb-6">
                  Seamlessly manage staff, clients, and freelancers with sophisticated role-based permissions.
                  Control access levels and maintain security across your entire practice.
                </p>

                {/* Feature highlights */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center gap-3">
                    <UserCheck className="h-5 w-5 opacity-80" />
                    <span className="text-sm opacity-90">Role-based Access</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Shield className="h-5 w-5 opacity-80" />
                    <span className="text-sm opacity-90">Security Controls</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Settings className="h-5 w-5 opacity-80" />
                    <span className="text-sm opacity-90">Custom Permissions</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Activity className="h-5 w-5 opacity-80" />
                    <span className="text-sm opacity-90">Activity Tracking</span>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex -space-x-2">
                    <div className="w-8 h-8 bg-white/30 rounded-full border-2 border-white/50"></div>
                    <div className="w-8 h-8 bg-white/30 rounded-full border-2 border-white/50"></div>
                    <div className="w-8 h-8 bg-white/30 rounded-full border-2 border-white/50"></div>
                  </div>
                  <span className="text-sm opacity-75">+1,200 practices</span>
                </div>
              </div>
            </motion.div>

            {/* Patient Records - Enhanced Tall Card */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              className="lg:col-span-4 bg-white p-8 rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-500 group"
            >
              <div className="p-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl text-white mb-6 w-fit group-hover:scale-110 transition-transform duration-300">
                <Stethoscope className="h-10 w-10" />
              </div>
              <h3 className="text-2xl font-bold mb-4 text-gray-800">Patient Records</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Complete EMR system with comprehensive medical histories, treatment plans, and real-time updates.
              </p>

              {/* Enhanced features list */}
              <div className="space-y-4 mb-6">
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <Database className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-medium text-gray-800 text-sm">Digital Health Records</div>
                    <div className="text-xs text-gray-600">Comprehensive patient histories</div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <Activity className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-medium text-gray-800 text-sm">Treatment Tracking</div>
                    <div className="text-xs text-gray-600">Real-time progress monitoring</div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <Camera className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-medium text-gray-800 text-sm">Medical Imaging</div>
                    <div className="text-xs text-gray-600">X-rays, ultrasounds, photos</div>
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Records Managed</span>
                  <span className="text-2xl font-bold text-green-600">50K+</span>
                </div>
              </div>
            </motion.div>

            {/* Smart Scheduling - Enhanced Wide Card */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="lg:col-span-6 bg-gradient-to-br from-purple-500 via-pink-500 to-rose-500 p-8 rounded-3xl text-white relative overflow-hidden group hover:scale-[1.02] transition-all duration-500"
            >
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="relative z-10">
                <div className="flex items-start justify-between mb-6">
                  <div className="p-4 bg-white/20 rounded-2xl backdrop-blur-sm">
                    <Brain className="h-10 w-10" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold">AI</div>
                    <div className="text-xs opacity-80">Powered</div>
                  </div>
                </div>
                <h3 className="text-2xl font-bold mb-4">Smart Scheduling</h3>
                <p className="opacity-90 leading-relaxed mb-6">
                  AI-powered appointment booking with intelligent conflict detection and automated reminders.
                </p>

                {/* Enhanced features */}
                <div className="grid grid-cols-2 gap-3 mb-6">
                  <div className="bg-white/20 rounded-lg p-3 backdrop-blur-sm">
                    <Zap className="h-5 w-5 mb-2" />
                    <div className="text-sm font-medium">Auto-booking</div>
                    <div className="text-xs opacity-80">Intelligent scheduling</div>
                  </div>
                  <div className="bg-white/20 rounded-lg p-3 backdrop-blur-sm">
                    <Shield className="h-5 w-5 mb-2" />
                    <div className="text-sm font-medium">Conflict Detection</div>
                    <div className="text-xs opacity-80">Prevent double booking</div>
                  </div>
                  <div className="bg-white/20 rounded-lg p-3 backdrop-blur-sm">
                    <Bell className="h-5 w-5 mb-2" />
                    <div className="text-sm font-medium">Auto Reminders</div>
                    <div className="text-xs opacity-80">SMS & Email alerts</div>
                  </div>
                  <div className="bg-white/20 rounded-lg p-3 backdrop-blur-sm">
                    <Smartphone className="h-5 w-5 mb-2" />
                    <div className="text-sm font-medium">Mobile Booking</div>
                    <div className="text-xs opacity-80">Client self-service</div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm opacity-80">Efficiency Increase</span>
                  <span className="text-xl font-bold">+40%</span>
                </div>
              </div>
            </motion.div>

            {/* Automated Billing - Enhanced Square Card */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="lg:col-span-3 bg-white p-8 rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-500 group"
            >
              <div className="p-4 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl text-white mb-6 w-fit group-hover:rotate-12 transition-transform duration-300">
                <CreditCard className="h-10 w-10" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">Automated Billing</h3>
              <p className="text-gray-600 text-sm leading-relaxed mb-4">
                Streamlined payment processing and invoicing with multiple payment options.
              </p>

              <div className="space-y-2 mb-4">
                <div className="flex items-center gap-2 text-xs text-gray-600">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  <span>Auto-invoice generation</span>
                </div>
                <div className="flex items-center gap-2 text-xs text-gray-600">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  <span>Payment tracking</span>
                </div>
                <div className="flex items-center gap-2 text-xs text-gray-600">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  <span>Insurance integration</span>
                </div>
              </div>

              <div className="bg-orange-50 rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-600">Processing Time</span>
                  <span className="text-sm font-bold text-orange-600">-75%</span>
                </div>
              </div>
            </motion.div>

            {/* Security - Enhanced Square Card */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="lg:col-span-3 bg-white p-8 rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-500 group"
            >
              <div className="p-4 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-2xl text-white mb-6 w-fit group-hover:-rotate-12 transition-transform duration-300">
                <Lock className="h-10 w-10" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">Enterprise Security</h3>
              <p className="text-gray-600 text-sm leading-relaxed mb-4">
                Enterprise-grade data protection and HIPAA compliance.
              </p>

              <div className="space-y-2 mb-4">
                <div className="flex items-center gap-2 text-xs text-gray-600">
                  <Shield className="h-3 w-3 text-indigo-500" />
                  <span>HIPAA Compliant</span>
                </div>
                <div className="flex items-center gap-2 text-xs text-gray-600">
                  <Shield className="h-3 w-3 text-indigo-500" />
                  <span>End-to-end encryption</span>
                </div>
                <div className="flex items-center gap-2 text-xs text-gray-600">
                  <Shield className="h-3 w-3 text-indigo-500" />
                  <span>Audit trails</span>
                </div>
              </div>

              <div className="bg-indigo-50 rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-600">Security Score</span>
                  <span className="text-sm font-bold text-indigo-600">A+</span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Comprehensive Features Section */}
      <section className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800" />

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-20">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              className="mb-6"
            >
              <span className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-purple-100 px-6 py-3 rounded-full text-sm font-semibold text-gray-700 border border-blue-200/50">
                <Layers className="h-4 w-4 text-blue-600" />
                Complete Feature Set
              </span>
            </motion.div>

            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-5xl md:text-6xl font-bold mb-6"
            >
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">Powerful Features</span>
              <br />
              <span className="text-gray-800">Built for Veterinarians</span>
            </motion.h2>
          </div>

          {/* Detailed Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Users,
                title: "Multi-Role Management",
                description: "Comprehensive user management with role-based permissions for staff, clients, and freelancers.",
                features: ["Role-based access control", "Custom permission sets", "Staff hierarchy management", "Freelancer integration"],
                color: "blue"
              },
              {
                icon: Brain,
                title: "AI-Powered Scheduling",
                description: "Intelligent appointment booking with conflict detection and automated optimization.",
                features: ["Smart conflict detection", "Automated reminders", "Resource optimization", "Mobile booking"],
                color: "purple"
              },
              {
                icon: Database,
                title: "Digital Health Records",
                description: "Complete EMR system with comprehensive patient histories and medical documentation.",
                features: ["Digital medical records", "Treatment tracking", "Medical imaging", "Lab results"],
                color: "green"
              },
              {
                icon: CreditCard,
                title: "Automated Billing",
                description: "Streamlined invoicing and payment processing with insurance integration.",
                features: ["Auto-invoice generation", "Payment tracking", "Insurance claims", "Financial reporting"],
                color: "orange"
              },
              {
                icon: BarChart3,
                title: "Analytics & Reporting",
                description: "Comprehensive business intelligence with detailed practice analytics.",
                features: ["Revenue analytics", "Patient insights", "Staff performance", "Custom reports"],
                color: "indigo"
              },
              {
                icon: Smartphone,
                title: "Mobile Access",
                description: "Full mobile compatibility for on-the-go practice management.",
                features: ["Mobile app", "Responsive design", "Offline access", "Push notifications"],
                color: "pink"
              }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white p-8 rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-500 group"
              >
                <div className={`p-4 bg-gradient-to-br ${
                  feature.color === 'blue' ? 'from-blue-500 to-blue-600' :
                  feature.color === 'purple' ? 'from-purple-500 to-purple-600' :
                  feature.color === 'green' ? 'from-green-500 to-green-600' :
                  feature.color === 'orange' ? 'from-orange-500 to-orange-600' :
                  feature.color === 'indigo' ? 'from-indigo-500 to-indigo-600' :
                  'from-pink-500 to-pink-600'
                } rounded-2xl text-white mb-6 w-fit group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="h-8 w-8" />
                </div>

                <h3 className="text-xl font-bold mb-3 text-gray-800">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed mb-6">{feature.description}</p>

                <div className="space-y-2">
                  {feature.features.map((item, idx) => (
                    <div key={idx} className="flex items-center gap-3">
                      <CheckCircle className={`h-4 w-4 ${
                        feature.color === 'blue' ? 'text-blue-500' :
                        feature.color === 'purple' ? 'text-purple-500' :
                        feature.color === 'green' ? 'text-green-500' :
                        feature.color === 'orange' ? 'text-orange-500' :
                        feature.color === 'indigo' ? 'text-indigo-500' :
                        'text-pink-500'
                      }`} />
                      <span className="text-sm text-gray-600">{item}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600" />
        <div className="absolute inset-0 bg-gradient-to-tr from-green-500/20 via-transparent to-blue-500/20" />

        {/* Animated Elements */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            rotate: [360, 180, 0]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-10 right-10 w-40 h-40 bg-white/10 rounded-full blur-xl"
        />

        <div className="max-w-5xl mx-auto text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >
            <span className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm px-6 py-3 rounded-full text-sm font-semibold text-white border border-white/30">
              <Sparkles className="h-4 w-4" />
              Transform Your Practice Today
              <Sparkles className="h-4 w-4" />
            </span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-5xl md:text-7xl font-bold text-white mb-8 leading-tight"
          >
            Ready to <span className="italic">Capture</span>
            <br />
            <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
              Souls?
            </span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl md:text-2xl text-white/90 mb-12 leading-relaxed max-w-3xl mx-auto"
          >
            Join <span className="font-bold text-yellow-300">thousands</span> of veterinary professionals who trust VetCare Pro
            to deliver <span className="font-bold text-yellow-300">exceptional care</span> with soul-capturing design
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
          >
            <Link href="/auth/login">
              <motion.button
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white text-gray-800 px-10 py-5 rounded-2xl text-lg font-bold shadow-2xl hover:shadow-3xl transition-all duration-300 flex items-center gap-3 relative overflow-hidden group"
              >
                <span className="relative z-10">Start Your Free Trial</span>
                <ArrowRight className="h-6 w-6 relative z-10 group-hover:translate-x-1 transition-transform duration-300" />
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-blue-50 to-purple-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                />
              </motion.button>
            </Link>

            <motion.button
              whileHover={{ scale: 1.05, y: -3 }}
              whileTap={{ scale: 0.95 }}
              className="border-2 border-white/50 text-white px-10 py-5 rounded-2xl text-lg font-bold hover:bg-white/10 hover:border-white transition-all duration-300 backdrop-blur-sm"
            >
              Schedule a Demo
            </motion.button>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="mt-16 flex flex-col sm:flex-row items-center justify-center gap-8 text-white/80"
          >
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-300" />
              <span className="text-sm">30-day free trial</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-300" />
              <span className="text-sm">No credit card required</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-300" />
              <span className="text-sm">Cancel anytime</span>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-transparent to-purple-900/20" />
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 60, repeat: Infinity, ease: "linear" }}
          className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full blur-3xl"
        />

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12 mb-12">
            {/* Brand Section */}
            <div className="md:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <div className="relative">
                  <PawPrint className="h-10 w-10 text-blue-400" />
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full"
                  />
                </div>
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  VetCare Pro
                </span>
              </div>
              <p className="text-gray-300 leading-relaxed mb-6 max-w-md">
                Transforming veterinary practices with soul-capturing design and comprehensive management tools.
                Built by veterinarians, for veterinarians.
              </p>
              <div className="flex items-center gap-4">
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center cursor-pointer"
                >
                  <Globe className="h-5 w-5" />
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center cursor-pointer"
                >
                  <Heart className="h-5 w-5" />
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center cursor-pointer"
                >
                  <Star className="h-5 w-5" />
                </motion.div>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-semibold mb-6 text-white">Quick Links</h4>
              <ul className="space-y-3">
                {['Features', 'Pricing', 'API Documentation', 'Support', 'Blog'].map((link) => (
                  <li key={link}>
                    <a href="#" className="text-gray-300 hover:text-blue-400 transition-colors duration-300 flex items-center gap-2 group">
                      <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      {link}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact */}
            <div>
              <h4 className="text-lg font-semibold mb-6 text-white">Get Started</h4>
              <div className="space-y-4">
                <Link href="/auth/login">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300"
                  >
                    Start Free Trial
                  </motion.button>
                </Link>
                <button className="w-full border border-gray-600 text-gray-300 px-6 py-3 rounded-xl font-semibold hover:border-blue-400 hover:text-blue-400 transition-all duration-300">
                  Schedule Demo
                </button>
              </div>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="border-t border-gray-700 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="text-gray-400 text-sm">
                © 2024 VetCare Pro. All rights reserved. Made with ❤️ for veterinarians.
              </div>
              <div className="flex items-center gap-6 text-sm text-gray-400">
                <a href="#" className="hover:text-blue-400 transition-colors duration-300">Privacy Policy</a>
                <a href="#" className="hover:text-blue-400 transition-colors duration-300">Terms of Service</a>
                <a href="#" className="hover:text-blue-400 transition-colors duration-300">Cookie Policy</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
