'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import {
  FiMapPin,
  FiPhone,
  FiMail,
  FiGlobe,
  FiUsers,
  FiSettings,
  FiEdit,
  FiArrowLeft,
  FiPlus,
  FiShield,
  FiHeart
} from 'react-icons/fi';
import { cn } from '@/lib/utils';
import { useThemeClasses, useClinicTheme } from '../../../contexts/ThemeContext';
import { useAuth } from '../../../contexts/AuthContext';
import { api, endpoints } from '../../../lib/api';
import MedicalLoader from '../../../components/common/MedicalLoader';
import { useRouter } from 'next/navigation';
import StaffManagementTab from '../components/StaffManagementTab';
import ServiceManagementTab from '../components/ServiceManagementTab';

interface Clinic {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  website?: string;
  description: string;
  isActive: boolean;
  branding: {
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
  };
  settings: {
    appointmentDuration: number;
    bookingAdvanceLimit: number;
    cancellationPolicy: string;
    timezone: string;
    currency: string;
    selectedSpecies: number[];
    selectedBreeds: number[];
    selectedServiceCategories: number[];
    selectedServices: number[];
  };
  owner: {
    firstName: string;
    lastName: string;
    email: string;
  };
  stats: {
    staffCount: number;
    patientCount: number;
    todayAppointmentCount: number;
  };
}

interface Staff {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: string;
  isActive: boolean;
  joinedAt: string;
  permissions: string[];
  specializations: string[];
}

function ClinicDetailsPageContent() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const themeClasses = useThemeClasses();
  const { colors } = useClinicTheme();
  const { user, hasRole } = useAuth();

  const clinicId = params.id as string;
  const activeTab = searchParams.get('tab') || 'overview';

  const [clinic, setClinic] = useState<Clinic | null>(null);
  const [staff, setStaff] = useState<Staff[]>([]);
  const [loading, setLoading] = useState(true);
  const [staffLoading, setStaffLoading] = useState(false);

  useEffect(() => {
    loadClinicData();
  }, [clinicId]);

  useEffect(() => {
    if (activeTab === 'staff') {
      loadStaffData();
    }
  }, [activeTab, clinicId]);

  const loadClinicData = async () => {
    try {
      setLoading(true);
      const response = await api.get(endpoints.clinics.getById(clinicId));
      
      if (response.data.success) {
        setClinic(response.data.data);
      }
    } catch (error) {
      console.error('Error loading clinic data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStaffData = async () => {
    try {
      setStaffLoading(true);
      const response = await api.get(`${endpoints.staff.list}?clinic=${clinicId}`);
      
      if (response.data.success) {
        setStaff(response.data.data);
      }
    } catch (error) {
      console.error('Error loading staff data:', error);
    } finally {
      setStaffLoading(false);
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FiMapPin },
    { id: 'staff', label: 'Staff', icon: FiUsers },
    { id: 'services', label: 'Services', icon: FiHeart },
    { id: 'settings', label: 'Settings', icon: FiSettings },
  ];

  if (loading) {
    return <MedicalLoader message="Loading clinic details..." fullScreen />;
  }

  if (!clinic) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Clinic Not Found</h2>
          <p className="text-gray-600">The clinic you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <FiArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {clinic.name}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {clinic.address.street}, {clinic.address.city}, {clinic.address.state}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => router.push(`/clinics/edit/${clinicId}`)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FiEdit className="w-4 h-4" />
                Edit Clinic
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 border-b">
        <div className="max-w-7xl mx-auto px-6">
          <nav className="flex space-x-8">
            {tabs.map((tab) => {
              const isActive = activeTab === tab.id;
              return (
                <button
                  key={tab.id}
                  onClick={() => router.push(`/clinics/${clinicId}?tab=${tab.id}`)}
                  className={cn(
                    'flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                    isActive
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )}
                >
                  <tab.icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-6 py-6">
        {activeTab === 'overview' && <OverviewTab clinic={clinic} />}
        {activeTab === 'staff' && (
          <StaffTab
            staff={staff}
            loading={staffLoading}
            clinicId={clinicId}
            onRefresh={loadStaffData}
            canEdit={hasRole('clinic_owner') || hasRole('admin')}
            isOwner={hasRole('clinic_owner')}
          />
        )}
        {activeTab === 'services' && (
          <ServiceManagementTab
            canEdit={hasRole('clinic_owner') || hasRole('admin')}
            isOwner={hasRole('clinic_owner')}
            clinicId={clinicId}
          />
        )}
        {activeTab === 'settings' && <SettingsTab clinic={clinic} />}
      </div>
    </div>
  );
}

// Overview Tab Component
function OverviewTab({ clinic }: { clinic: Clinic }) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Clinic Info */}
      <div className="lg:col-span-2 space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Clinic Information
          </h3>
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <FiMail className="w-5 h-5 text-gray-400" />
              <span className="text-gray-900 dark:text-white">{clinic.email}</span>
            </div>
            <div className="flex items-center gap-3">
              <FiPhone className="w-5 h-5 text-gray-400" />
              <span className="text-gray-900 dark:text-white">{clinic.phone}</span>
            </div>
            <div className="flex items-center gap-3">
              <FiMapPin className="w-5 h-5 text-gray-400" />
              <span className="text-gray-900 dark:text-white">
                {clinic.address.street}, {clinic.address.city}, {clinic.address.state} {clinic.address.zipCode}
              </span>
            </div>
            {clinic.website && (
              <div className="flex items-center gap-3">
                <FiGlobe className="w-5 h-5 text-gray-400" />
                <a 
                  href={clinic.website} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  {clinic.website}
                </a>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Description
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {clinic.description || 'No description available.'}
          </p>
        </div>
      </div>

      {/* Stats */}
      <div className="space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Statistics
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FiUsers className="w-4 h-4 text-gray-400" />
                <span className="text-gray-600 dark:text-gray-400">Staff</span>
              </div>
              <span className="font-semibold text-gray-900 dark:text-white">
                {clinic.stats.staffCount}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FiHeart className="w-4 h-4 text-gray-400" />
                <span className="text-gray-600 dark:text-gray-400">Patients</span>
              </div>
              <span className="font-semibold text-gray-900 dark:text-white">
                {clinic.stats.patientCount}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FiSettings className="w-4 h-4 text-gray-400" />
                <span className="text-gray-600 dark:text-gray-400">Today's Appointments</span>
              </div>
              <span className="font-semibold text-gray-900 dark:text-white">
                {clinic.stats.todayAppointmentCount}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Owner
          </h3>
          <div className="space-y-2">
            <p className="font-medium text-gray-900 dark:text-white">
              {clinic.owner.firstName} {clinic.owner.lastName}
            </p>
            <p className="text-gray-600 dark:text-gray-400">
              {clinic.owner.email}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// Staff Tab Component
function StaffTab({
  staff,
  loading,
  clinicId,
  onRefresh,
  canEdit,
  isOwner
}: {
  staff: Staff[];
  loading: boolean;
  clinicId: string;
  onRefresh: () => void;
  canEdit: boolean;
  isOwner: boolean;
}) {
  return (
    <StaffManagementTab
      canEdit={canEdit}
      isOwner={isOwner}
      clinicId={clinicId}
    />
  );
}

// Settings Tab Component
function SettingsTab({ clinic }: { clinic: Clinic }) {
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
        Clinic Settings
      </h2>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* General Settings */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            General Settings
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Appointment Duration (minutes)
              </label>
              <input
                type="number"
                value={clinic.settings.appointmentDuration}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                readOnly
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Booking Advance Limit (days)
              </label>
              <input
                type="number"
                value={clinic.settings.bookingAdvanceLimit}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                readOnly
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Timezone
              </label>
              <input
                type="text"
                value={clinic.settings.timezone}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                readOnly
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Currency
              </label>
              <input
                type="text"
                value={clinic.settings.currency}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                readOnly
              />
            </div>
          </div>
        </div>

        {/* Branding */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Branding
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Primary Color
              </label>
              <div className="flex items-center gap-3">
                <div
                  className="w-10 h-10 rounded-md border"
                  style={{ backgroundColor: clinic.branding.primaryColor }}
                />
                <input
                  type="text"
                  value={clinic.branding.primaryColor}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  readOnly
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Secondary Color
              </label>
              <div className="flex items-center gap-3">
                <div
                  className="w-10 h-10 rounded-md border"
                  style={{ backgroundColor: clinic.branding.secondaryColor }}
                />
                <input
                  type="text"
                  value={clinic.branding.secondaryColor}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  readOnly
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Accent Color
              </label>
              <div className="flex items-center gap-3">
                <div
                  className="w-10 h-10 rounded-md border"
                  style={{ backgroundColor: clinic.branding.accentColor }}
                />
                <input
                  type="text"
                  value={clinic.branding.accentColor}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  readOnly
                />
              </div>
            </div>
          </div>
        </div>

        {/* Policies */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Policies
          </h3>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Cancellation Policy
            </label>
            <textarea
              value={clinic.settings.cancellationPolicy}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              readOnly
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ClinicDetailsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ClinicDetailsPageContent />
    </Suspense>
  );
}
