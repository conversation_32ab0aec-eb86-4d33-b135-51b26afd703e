'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { api, endpoints } from '../lib/api';
import { dataPreloader } from '../lib/preloader';

// Storage utilities for data persistence
const STORAGE_KEYS = {
  USER_DATA: 'vet_user_data',
  PROFILE_DATA: 'vet_profile_data',
  CLINIC_MANAGEMENT: 'vet_clinic_management',
  CURRENT_CLINIC: 'vet_current_clinic',
  AVAILABLE_CLINICS: 'vet_available_clinics',
  DATA_TIMESTAMP: 'vet_data_timestamp'
};

const DATA_EXPIRY_TIME = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

const saveToStorage = (key: string, data: any) => {
  try {
    const timestamp = Date.now();
    const storageData = {
      data,
      timestamp
    };
    localStorage.setItem(key, JSON.stringify(storageData));
  } catch (error) {
    console.error(`Error saving ${key} to storage:`, error);
  }
};

const loadFromStorage = (key: string) => {
  try {
    const stored = localStorage.getItem(key);
    if (!stored) return null;

    const { data, timestamp } = JSON.parse(stored);

    // Check if data is expired
    if (Date.now() - timestamp > DATA_EXPIRY_TIME) {
      localStorage.removeItem(key);
      return null;
    }

    return data;
  } catch (error) {
    console.error(`Error loading ${key} from storage:`, error);
    localStorage.removeItem(key);
    return null;
  }
};

const clearAllStoredData = () => {
  Object.values(STORAGE_KEYS).forEach(key => {
    localStorage.removeItem(key);
  });
};

// Debug function to check stored data
const debugStoredData = () => {
  console.log('=== STORED DATA DEBUG ===');
  Object.entries(STORAGE_KEYS).forEach(([name, key]) => {
    const data = loadFromStorage(key);
    console.log(`${name}:`, data ? 'STORED' : 'NOT STORED', data ? `(${JSON.stringify(data).length} chars)` : '');
  });
  console.log('========================');
};

export interface UserProfile {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  avatar?: string;
  isActive: boolean;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  lastLogin?: string;
  createdAt: string;

  // Role information
  role: number | string; // Support both old string and new number format
  roleDetails?: {
    id: number;
    name: string;
    displayName: string;
    permissions: number[];
    level: number;
  };
  
  // Profile details
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  emergencyContact?: {
    name?: string;
    phone?: string;
    relationship?: string;
  };
  
  // Professional details (for staff/freelancers)
  licenseNumber?: string;
  specializations?: string[];
  education?: Array<{
    degree: string;
    institution: string;
    year: number;
  }>;
  experience?: Array<{
    position: string;
    organization: string;
    startDate: string;
    endDate?: string;
    description?: string;
  }>;
  certifications?: Array<{
    name: string;
    issuingOrganization: string;
    issueDate: string;
    expiryDate?: string;
  }>;
  
  // Clinic roles
  clinicRoles?: Array<{
    _id: string;
    clinic: {
      _id: string;
      name: string;
      email: string;
      phone: string;
    };
    role: {
      _id: string;
      name: string;
      displayName: string;
      permissions: string[];
    };
    permissions: string[];
    isActive: boolean;
    startDate: string;
    endDate?: string;
  }>;
  
  // Preferences
  preferences?: {
    theme?: 'light' | 'dark' | 'system';
    language?: string;
    timezone?: string;
    notifications?: {
      email: boolean;
      sms: boolean;
      push: boolean;
      appointments: boolean;
      reminders: boolean;
      marketing: boolean;
    };
  };
}

interface AuthContextType {
  user: UserProfile | null;
  profile: any | null;
  viewingProfile: any | null;
  clinicManagement: any | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  currentClinic: any | null;
  availableClinics: any[];
  login: (token: string, userData?: UserProfile) => void;
  logout: () => void;
  updateUser: (userData: Partial<UserProfile>) => void;
  refreshUser: () => Promise<void>;
  loadProfile: () => Promise<void>;
  loadUserProfile: (userId: string, clinicId?: string) => Promise<void>;
  loadClinicManagement: () => Promise<void>;
  getCurrentClinic: () => any | null;
  switchClinic: (clinicId: string) => Promise<void>;
  loadUserClinics: () => Promise<void>;
  loadFullUserData: () => Promise<void>;
  hasPermission: (permission: string, clinicId?: string) => boolean;
  hasRole: (role: string, clinicId?: string) => boolean;
  debugStoredData: () => void;
  isAdmin: () => boolean;
  canAccessPage: (page: string) => boolean;
  canPerformAction: (action: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [profile, setProfile] = useState<any | null>(null);
  const [viewingProfile, setViewingProfile] = useState<any | null>(null);
  const [clinicManagement, setClinicManagement] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentClinic, setCurrentClinic] = useState<any | null>(null);
  const [availableClinics, setAvailableClinics] = useState<any[]>([]);

  const isAuthenticated = !!user && !!localStorage.getItem('authToken');

  // Load user data on mount - optimized for faster loading
  useEffect(() => {
    const token = localStorage.getItem('authToken');
    console.log('AuthContext useEffect - token found:', !!token);
    if (token) {
      // Load from storage first for immediate display
      const hasStoredData = loadDataFromStorage();
      if (hasStoredData) {
        setIsLoading(false); // Show UI immediately if we have cached data
      }
      // Then refresh from API in background
      refreshUser();
    } else {
      console.log('No auth token found, setting loading to false');
      setIsLoading(false);
    }
  }, []);

  // Load data from localStorage for immediate display - optimized
  const loadDataFromStorage = () => {
    try {
      const storedUser = loadFromStorage(STORAGE_KEYS.USER_DATA);
      const storedProfile = loadFromStorage(STORAGE_KEYS.PROFILE_DATA);
      const storedClinicManagement = loadFromStorage(STORAGE_KEYS.CLINIC_MANAGEMENT);
      const storedCurrentClinic = loadFromStorage(STORAGE_KEYS.CURRENT_CLINIC);
      const storedAvailableClinics = loadFromStorage(STORAGE_KEYS.AVAILABLE_CLINICS);

      let hasData = false;

      if (storedUser) {
        setUser(storedUser);
        hasData = true;
        console.log('Loaded user from storage:', storedUser.firstName, storedUser.lastName);
      }
      if (storedProfile) {
        setProfile(storedProfile);
        hasData = true;
        console.log('Loaded profile from storage');
      }
      if (storedClinicManagement) {
        setClinicManagement(storedClinicManagement);
        console.log('Loaded clinic management from storage');
      }
      if (storedCurrentClinic) {
        setCurrentClinic(storedCurrentClinic);
        console.log('Loaded current clinic from storage:', storedCurrentClinic.clinic?.name);
      }
      if (storedAvailableClinics) {
        setAvailableClinics(storedAvailableClinics);
        console.log('Loaded available clinics from storage:', storedAvailableClinics.length, 'clinics');
      }

      return hasData;
    } catch (error) {
      console.error('Error loading data from storage:', error);
      return false;
    }
  };

  const login = (token: string, userData?: UserProfile) => {
    localStorage.setItem('authToken', token);

    // Start preloading critical data immediately
    dataPreloader.preloadCriticalData(token);

    if (userData) {
      setUser(userData);
      saveToStorage(STORAGE_KEYS.USER_DATA, userData);
      console.log('Saved user data to storage during login:', userData.firstName, userData.lastName);

      // Preload dashboard data in background
      dataPreloader.preloadDashboardData(token);
    } else {
      refreshUser();
    }
  };

  const logout = () => {
    localStorage.removeItem('authToken');
    clearAllStoredData();
    dataPreloader.clearCache(); // Clear preloader cache
    setUser(null);
    setProfile(null);
    setClinicManagement(null);
    setCurrentClinic(null);
    setAvailableClinics([]);
    console.log('Cleared all stored data during logout');
    window.location.href = '/auth/login';
  };

  const updateUser = (userData: Partial<UserProfile>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      saveToStorage(STORAGE_KEYS.USER_DATA, updatedUser);
      console.log('Updated and saved user data to storage');
    }
  };

  const refreshUser = async () => {
    if (isLoading) {
      console.log('refreshUser called but already loading, skipping');
      return;
    }

    try {
      console.log('refreshUser called - starting API call to /auth/me');
      setIsLoading(true);
      const response = await api.get(endpoints.auth.me);
      const data = response.data;
      console.log('refreshUser API response:', data);

      if (data.success) {
        const userData = data.data.user || data.data;
        const currentClinicData = data.data.currentClinic || null;

        setUser(userData);
        setCurrentClinic(currentClinicData);

        // Save to storage
        saveToStorage(STORAGE_KEYS.USER_DATA, userData);
        if (currentClinicData) {
          saveToStorage(STORAGE_KEYS.CURRENT_CLINIC, currentClinicData);
        }

        console.log('Refreshed and saved user data:', userData.firstName, userData.lastName);

        // Load available clinics and full profile after setting user
        if (userData) {
          await loadFullUserData();
        }
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error('Failed to refresh user:', error);
      logout();
    } finally {
      setIsLoading(false);
    }
  };

  const loadFullUserData = async () => {
    try {
      await Promise.all([
        loadUserClinics(),
        loadProfile()
      ]);
      // Load clinic management data if user has a current clinic
      if (currentClinic) {
        await loadClinicManagement();
      }
    } catch (error) {
      console.error('Failed to load full user data:', error);
    }
  };

  const loadProfile = async () => {
    try {
      const response = await api.get(endpoints.profile.me);
      const data = response.data;

      if (data.success) {
        setProfile(data.data);
        saveToStorage(STORAGE_KEYS.PROFILE_DATA, data.data);
        console.log('Loaded and saved profile data to storage');

        // Update current clinic from profile if available
        if (data.data.currentClinic) {
          setCurrentClinic(data.data.currentClinic);
          saveToStorage(STORAGE_KEYS.CURRENT_CLINIC, data.data.currentClinic);
          // Load clinic management data when clinic is set
          loadClinicManagement();
        }
      }
    } catch (error) {
      console.error('Failed to load profile:', error);
    }
  };

  const loadUserProfile = async (userId: string, clinicId?: string) => {
    try {
      const url = clinicId
        ? `${endpoints.profile.user(userId)}?clinicId=${clinicId}`
        : endpoints.profile.user(userId);

      const response = await api.get(url);
      const data = response.data;

      if (data.success) {
        setViewingProfile(data.data);
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error('Failed to load user profile:', error);
      throw error;
    }
  };

  const loadClinicManagement = async () => {
    try {
      const response = await api.get(endpoints.clinics.management);
      const data = response.data;

      if (data.success) {
        setClinicManagement(data.data);
        saveToStorage(STORAGE_KEYS.CLINIC_MANAGEMENT, data.data);
        console.log('Loaded and saved clinic management data to storage');
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error('Failed to load clinic management data:', error);
      // Don't throw error here as this is not critical for basic functionality
    }
  };

  const loadUserClinics = async () => {
    try {
      // Call real backend API for user clinics
      const response = await api.get(endpoints.auth.clinics);
      const data = response.data;

      if (data.success) {
        const userClinics = data.data || [];
        setAvailableClinics(userClinics);
        saveToStorage(STORAGE_KEYS.AVAILABLE_CLINICS, userClinics);
        console.log('Loaded and saved available clinics to storage:', userClinics.length, 'clinics');

        // Set appropriate default clinic if not already set
        if (!currentClinic && userClinics.length > 0) {
          // Priority 1: Find clinic where user is owner
          const ownedClinic = userClinics.find((clinic: any) =>
            clinic.role?.name === 'clinic_owner' ||
            clinic.role?.name === 'owner'
          );

          let selectedClinic = null;
          if (ownedClinic) {
            selectedClinic = ownedClinic;
            console.log('Set default clinic to owned clinic:', ownedClinic.clinic?.name);
          } else {
            // Priority 2: Use first available clinic (staff clinic)
            selectedClinic = userClinics[0];
            console.log('Set default clinic to first available:', userClinics[0].clinic?.name);
          }

          if (selectedClinic) {
            setCurrentClinic(selectedClinic);
            saveToStorage(STORAGE_KEYS.CURRENT_CLINIC, selectedClinic);
          }
        }
      } else {
        throw new Error(data.message || 'Failed to load user clinics');
      }
    } catch (error) {
      console.error('Failed to load user clinics:', error);
      // Fallback to empty array if API fails
      setAvailableClinics([]);
    }
  };

  const switchClinic = async (clinicId: string) => {
    try {
      const response = await api.post(endpoints.auth.switchClinic, { clinicId });
      const data = response.data;

      if (data.success) {
        setUser(data.data.user);
        setCurrentClinic(data.data.currentClinic);

        // Save updated data to storage
        saveToStorage(STORAGE_KEYS.USER_DATA, data.data.user);
        saveToStorage(STORAGE_KEYS.CURRENT_CLINIC, data.data.currentClinic);
        console.log('Switched clinic and saved to storage:', data.data.currentClinic?.clinic?.name);

        // Refresh available clinics and clinic management data
        await Promise.all([
          loadUserClinics(),
          loadClinicManagement()
        ]);
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error('Failed to switch clinic:', error);
      throw error;
    }
  };

  const getCurrentClinic = () => {
    return currentClinic || (profile?.user?.clinicRoles && profile.user.clinicRoles.length > 0
      ? profile.user.clinicRoles[0]
      : null);
  };

  const hasPermission = (permission: string, clinicId?: string) => {
    // Admin always has all permissions
    if (user?.email === '<EMAIL>' || hasRole('admin')) {
      return true;
    }

    if (!profile?.user?.clinicRoles) return false;

    const relevantRoles = clinicId
      ? profile.user.clinicRoles.filter(role => role.clinic.id === clinicId && role.isActive)
      : profile.user.clinicRoles.filter(role => role.isActive);

    return relevantRoles.some(role =>
      role.permissions.includes('all') ||
      role.permissions.includes(permission) ||
      role.role.permissions.includes('all') ||
      role.role.permissions.includes(permission)
    );
  };

  const hasRole = (roleName: string, clinicId?: string) => {
    // Admin always has admin role
    if (user?.email === '<EMAIL>' && roleName === 'admin') {
      return true;
    }

    // Check primary user role first (new system)
    if (user?.roleDetails?.name === roleName) {
      return true;
    }

    // Fallback to clinic roles (for staff assignments)
    if (!profile?.user?.clinicRoles) return false;

    const relevantRoles = clinicId
      ? profile.user.clinicRoles.filter(role => role.clinic.id === clinicId && role.isActive)
      : profile.user.clinicRoles.filter(role => role.isActive);

    return relevantRoles.some(role =>
      role.role.name === roleName ||
      role.role.displayName === roleName
    );
  };

  const isAdmin = () => {
    return user?.email === '<EMAIL>' ||
           user?.roleDetails?.name === 'admin' ||
           hasRole('admin');
  };

  const canAccessPage = (page: string) => {
    // Admin can access all pages
    if (isAdmin()) return true;

    // For now, allow access to all pages for authenticated users
    // This can be refined later with specific page permissions
    return isAuthenticated;
  };

  const canPerformAction = (action: string) => {
    // Admin can perform all actions
    if (isAdmin()) return true;

    // Check specific permission
    return hasPermission(action);
  };

  const value = {
    user,
    profile,
    viewingProfile,
    clinicManagement,
    isAuthenticated,
    isLoading,
    currentClinic,
    availableClinics,
    login,
    logout,
    updateUser,
    refreshUser,
    loadProfile,
    loadUserProfile,
    loadClinicManagement,
    getCurrentClinic,
    switchClinic,
    loadUserClinics,
    loadFullUserData,
    hasPermission,
    hasRole,
    debugStoredData,
    isAdmin,
    canAccessPage,
    canPerformAction,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Higher-order component for protected routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useAuth();

    if (isLoading) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    if (!isAuthenticated) {
      window.location.href = '/auth/login';
      return null;
    }

    return <Component {...props} />;
  };
}

// Hook for role-based access
export function usePermissions(clinicId?: string) {
  const { hasPermission, hasRole, user } = useAuth();
  
  return {
    hasPermission: (permission: string) => hasPermission(permission, clinicId),
    hasRole: (role: string) => hasRole(role, clinicId),
    isOwner: hasRole('clinic_owner', clinicId),
    isManager: hasRole('manager', clinicId),
    isStaff: hasRole('vet', clinicId) || hasRole('vet_assistant', clinicId) || hasRole('receptionist', clinicId) || hasRole('staff', clinicId),
    isFreelancer: hasRole('freelancer', clinicId),
    canManageStaff: hasPermission('staff_management', clinicId),
    canManageClinic: hasPermission('clinic_settings', clinicId),
    canViewReports: hasPermission('reports', clinicId),
    user
  };
}
