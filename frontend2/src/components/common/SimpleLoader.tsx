'use client';

import React from 'react';
import { Heart } from 'lucide-react';
import { useThemeClasses, useClinicTheme } from '../../contexts/ThemeContext';
import { cn } from '@/lib/utils';

interface SimpleLoaderProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  fullScreen?: boolean;
}

const SimpleLoader: React.FC<SimpleLoaderProps> = ({ 
  message = 'Loading...', 
  size = 'md',
  fullScreen = false 
}) => {
  const themeClasses = useThemeClasses();
  const { colors } = useClinicTheme();

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  const containerClass = fullScreen 
    ? 'fixed inset-0 z-50 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm'
    : 'flex items-center justify-center p-8';

  return (
    <div className={containerClass}>
      <div className="flex flex-col items-center justify-center space-y-4">
        {/* Simple spinning heart */}
        <div className="relative">
          <Heart 
            className={cn(
              sizeClasses[size], 
              'animate-pulse text-blue-500'
            )}
            style={{ color: colors.primary }}
          />
        </div>
        
        {/* Loading message */}
        <div className="text-center">
          <p className={cn(
            'text-gray-600 dark:text-gray-300 font-medium',
            size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base'
          )}>
            {message}
          </p>
        </div>
      </div>
    </div>
  );
};

export default SimpleLoader;
