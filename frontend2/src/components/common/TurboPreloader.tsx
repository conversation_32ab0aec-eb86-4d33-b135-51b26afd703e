'use client';

import { useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { turboPreloader } from '../../lib/preloader';

/**
 * TurboPreloader component that initializes preloading strategies
 * for better performance with Turbopack
 */
export default function TurboPreloader() {
  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    // Initialize Turbo preloader on mount
    const initializePreloader = async () => {
      try {
        const token = localStorage.getItem('authToken');
        
        if (isAuthenticated && token) {
          // Initialize with authentication
          await turboPreloader.initializeTurboPreloader(token);
        } else {
          // Initialize without authentication (preload components only)
          await turboPreloader.preloadCriticalComponents();
        }

        // Setup interaction-based preloading for additional resources
        turboPreloader.setupInteractionPreloading(token || undefined);
        
      } catch (error) {
        console.warn('Turbo preloader initialization failed:', error);
      }
    };

    // Delay initialization slightly to not block initial render
    const timeoutId = setTimeout(initializePreloader, 100);

    return () => clearTimeout(timeoutId);
  }, [isAuthenticated, user]);

  // This component doesn't render anything
  return null;
}

/**
 * Hook for manual preloading control
 */
export function useTurboPreloader() {
  const { user, isAuthenticated } = useAuth();

  const preloadDashboard = async () => {
    const token = localStorage.getItem('authToken');
    if (token) {
      await turboPreloader.preloadDashboardData(token);
    }
  };

  const preloadProfile = async () => {
    const token = localStorage.getItem('authToken');
    if (token) {
      await turboPreloader.preloadCriticalData(token);
    }
  };

  const preloadComponents = async () => {
    await turboPreloader.preloadCriticalComponents();
  };

  return {
    preloadDashboard,
    preloadProfile,
    preloadComponents,
    isAuthenticated,
  };
}
