'use client';

import React from 'react';
import { Heart, Activity } from 'lucide-react';
import { useThemeClasses, useClinicTheme } from '../../contexts/ThemeContext';
import { cn } from '@/lib/utils';

interface MedicalLoaderProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  fullScreen?: boolean;
}

const MedicalLoader: React.FC<MedicalLoaderProps> = ({ 
  message = 'Loading...', 
  size = 'md',
  fullScreen = false 
}) => {
  const themeClasses = useThemeClasses();
  const { colors } = useClinicTheme();

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  const containerSizeClasses = {
    sm: 'w-32 h-32',
    md: 'w-48 h-48',
    lg: 'w-64 h-64'
  };

  // Simplified loader with minimal animations

  const containerClass = fullScreen
    ? 'fixed inset-0 z-50 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm'
    : 'flex items-center justify-center p-8';

  return (
    <div className={containerClass}>
      <div className={cn('relative flex flex-col items-center justify-center', containerSizeClasses[size])}>
        {/* Simplified static background */}
        <div className="absolute inset-0 flex items-center justify-center z-0">
          <div
            style={{ borderColor: colors.primary }}
            className="absolute w-20 h-20 border-2 rounded-full opacity-30"
          />
        </div>

        {/* Central heartbeat pulse */}
        <div className="relative z-10 flex items-center justify-center">
          <motion.div
            variants={heartbeatVariants}
            animate="animate"
            className="relative"
          >
            {/* Central heart */}
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center shadow-lg relative z-20"
              style={{ backgroundColor: colors.primary }}
            >
              <Heart className="h-8 w-8 text-white" />
            </div>
          </motion.div>
        </div>

        {/* Floating medical icons */}
        <div className="absolute inset-0 z-5">
          {medicalIcons.map(({ Icon, color, delay, duration }, index) => {
            const angle = (index * 360) / medicalIcons.length;
            const radius = size === 'lg' ? 90 : size === 'md' ? 70 : 50;
            const x = Math.cos((angle * Math.PI) / 180) * radius;
            const y = Math.sin((angle * Math.PI) / 180) * radius;

            return (
              <motion.div
                key={index}
                variants={floatingVariants}
                animate="animate"
                custom={duration}
                className="absolute z-5"
                style={{
                  left: '50%',
                  top: '50%',
                  transform: `translate(calc(-50% + ${x}px), calc(-50% + ${y}px))`
                }}
                initial={{ opacity: 0, scale: 0 }}
                animate={{
                  opacity: [0, 0.8, 0.8, 0],
                  scale: [0, 1, 1, 0],
                  transition: {
                    duration: 4,
                    repeat: Infinity,
                    delay: delay,
                    ease: "easeInOut"
                  }
                }}
              >
                <div
                  className="p-2 rounded-full shadow-md border"
                  style={{
                    backgroundColor: `${color}15`,
                    borderColor: `${color}40`
                  }}
                >
                  <Icon
                    className={sizeClasses[size]}
                    style={{ color }}
                  />
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* EKG line animation */}
        <div className="absolute bottom-4 left-0 right-0 h-6 overflow-hidden z-5">
          <svg className="w-full h-full" viewBox="0 0 200 24">
            <motion.path
              d="M0 12 L40 12 L45 6 L50 18 L55 3 L60 21 L65 12 L200 12"
              stroke={colors.primary}
              strokeWidth="1.5"
              fill="none"
              variants={pathVariants}
              animate="animate"
              style={{ pathLength: 0 }}
              opacity={0.6}
            />
          </svg>
        </div>

        {/* Loading message */}
        <div className="absolute -bottom-20 left-1/2 transform -translate-x-1/2 text-center z-30">
          <motion.p
            className={cn('text-sm font-medium whitespace-nowrap', themeClasses.text)}
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            {message}
          </motion.p>

          {/* Loading dots */}
          <div className="flex justify-center space-x-1 mt-2">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="w-2 h-2 rounded-full"
                style={{ backgroundColor: colors.primary }}
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MedicalLoader;
