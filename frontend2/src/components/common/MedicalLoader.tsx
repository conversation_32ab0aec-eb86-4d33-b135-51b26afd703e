'use client';

import React from 'react';
import { Heart, Activity } from 'lucide-react';
import { useThemeClasses, useClinicTheme } from '../../contexts/ThemeContext';
import { cn } from '@/lib/utils';

interface MedicalLoaderProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  fullScreen?: boolean;
}

const MedicalLoader: React.FC<MedicalLoaderProps> = ({ 
  message = 'Loading...', 
  size = 'md',
  fullScreen = false 
}) => {
  const themeClasses = useThemeClasses();
  const { colors } = useClinicTheme();

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  const containerSizeClasses = {
    sm: 'w-32 h-32',
    md: 'w-48 h-48',
    lg: 'w-64 h-64'
  };

  // Simplified loader with minimal animations

  const containerClass = fullScreen
    ? 'fixed inset-0 z-50 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm'
    : 'flex items-center justify-center p-8';

  return (
    <div className={containerClass}>
      <div className={cn('relative flex flex-col items-center justify-center', containerSizeClasses[size])}>
        {/* Simplified static background */}
        <div className="absolute inset-0 flex items-center justify-center z-0">
          <div
            style={{ borderColor: colors.primary }}
            className="absolute w-20 h-20 border-2 rounded-full opacity-30"
          />
        </div>

        {/* Central heartbeat pulse - simplified */}
        <div className="relative z-10 flex items-center justify-center">
          <div className="relative">
            {/* Central heart */}
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center shadow-lg relative z-20 animate-pulse"
              style={{ backgroundColor: colors.primary }}
            >
              <Heart className="h-8 w-8 text-white" />
            </div>
          </div>
        </div>

        {/* Simplified loading message */}
        <div className="absolute -bottom-20 left-1/2 transform -translate-x-1/2 text-center z-30">
          <p className={cn('text-sm font-medium whitespace-nowrap animate-pulse', themeClasses.text)}>
            {message}
          </p>

          {/* Simple loading dots */}
          <div className="flex justify-center space-x-1 mt-2">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="w-2 h-2 rounded-full animate-bounce"
                style={{
                  backgroundColor: colors.primary,
                  animationDelay: `${i * 0.2}s`
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MedicalLoader;
