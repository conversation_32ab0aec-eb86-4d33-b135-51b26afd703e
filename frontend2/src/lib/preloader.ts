// Enhanced preloader utility for heavy components and data with Turbo optimizations
import { api } from './api';

interface PreloadedData {
  profile?: any;
  clinics?: any;
  patients?: any;
  appointments?: any;
  staff?: any;
  clients?: any;
}

class DataPreloader {
  private cache: Map<string, any> = new Map();
  private loadingPromises: Map<string, Promise<any>> = new Map();
  private preloadedData: PreloadedData = {};

  // Preload critical user data immediately after login
  async preloadCriticalData(token: string) {
    if (!token) return;

    const criticalEndpoints = [
      { key: 'profile', endpoint: '/profile/me' },
      { key: 'clinics', endpoint: '/auth/clinics' },
    ];

    const promises = criticalEndpoints.map(async ({ key, endpoint }) => {
      try {
        const data = await api.get(endpoint);
        this.cache.set(key, data);
        this.preloadedData[key as keyof PreloadedData] = data;
        return { key, data };
      } catch (error) {
        console.warn(`Failed to preload ${key}:`, error);
        return { key, data: null };
      }
    });

    await Promise.allSettled(promises);
  }

  // Preload dashboard data in background
  async preloadDashboardData(token: string) {
    if (!token) return;

    const dashboardEndpoints = [
      { key: 'patients', endpoint: '/patients?limit=5' },
      { key: 'appointments', endpoint: '/appointments?limit=10' },
      { key: 'staff', endpoint: '/staff?limit=10' },
    ];

    // Don't await these - let them load in background
    dashboardEndpoints.forEach(async ({ key, endpoint }) => {
      try {
        const data = await api.get(endpoint);
        this.cache.set(key, data);
        this.preloadedData[key as keyof PreloadedData] = data;
      } catch (error) {
        console.warn(`Failed to preload ${key}:`, error);
      }
    });
  }

  // Get cached data or fetch if not available
  async getData(key: string, endpoint: string, force = false): Promise<any> {
    if (!force && this.cache.has(key)) {
      return this.cache.get(key);
    }

    // Check if already loading
    if (this.loadingPromises.has(key)) {
      return this.loadingPromises.get(key);
    }

    // Start loading
    const promise = api.get(endpoint).then(data => {
      this.cache.set(key, data);
      this.loadingPromises.delete(key);
      return data;
    }).catch(error => {
      this.loadingPromises.delete(key);
      throw error;
    });

    this.loadingPromises.set(key, promise);
    return promise;
  }

  // Get preloaded data immediately (synchronous)
  getPreloadedData(key: keyof PreloadedData) {
    return this.preloadedData[key] || this.cache.get(key);
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
    this.loadingPromises.clear();
    this.preloadedData = {};
  }

  // Preload specific page data
  async preloadPageData(page: string) {
    const pageEndpoints: Record<string, Array<{ key: string; endpoint: string }>> = {
      patients: [
        { key: 'patients-full', endpoint: '/patients' },
        { key: 'species', endpoint: '/species' },
        { key: 'breeds', endpoint: '/breeds' },
      ],
      appointments: [
        { key: 'appointments-full', endpoint: '/appointments' },
        { key: 'services', endpoint: '/services' },
        { key: 'patients-list', endpoint: '/patients?limit=100' },
      ],
      staff: [
        { key: 'staff-full', endpoint: '/staff' },
        { key: 'roles', endpoint: '/roles' },
      ],
      clients: [
        { key: 'clients-full', endpoint: '/clients' },
      ],
    };

    const endpoints = pageEndpoints[page];
    if (!endpoints) return;

    // Preload in background
    endpoints.forEach(async ({ key, endpoint }) => {
      try {
        await this.getData(key, endpoint);
      } catch (error) {
        console.warn(`Failed to preload ${page} data:`, error);
      }
    });
  }
}

export const dataPreloader = new DataPreloader();

// Hook for using preloaded data
export function usePreloadedData<T = any>(key: string, endpoint: string, fallbackData?: T) {
  const [data, setData] = React.useState<T | null>(
    dataPreloader.getPreloadedData(key) || fallbackData || null
  );
  const [loading, setLoading] = React.useState(!data);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (data) {
      setLoading(false);
      return;
    }

    dataPreloader.getData(key, endpoint)
      .then(result => {
        setData(result);
        setLoading(false);
      })
      .catch(err => {
        setError(err.message);
        setLoading(false);
      });
  }, [key, endpoint]);

  return { data, loading, error, refetch: () => dataPreloader.getData(key, endpoint, true) };
}

// React import for the hook
import React from 'react';

// Turbo-specific preloading enhancements
class TurboPreloader extends DataPreloader {
  private componentCache: Map<string, any> = new Map();

  // Preload critical components for Turbo
  async preloadCriticalComponents() {
    if (typeof window === 'undefined') return;

    const criticalComponents = [
      () => import('../components/layout/DashboardLayout'),
      () => import('../components/common/SimpleLoader'),
      () => import('../components/common/DashboardSkeleton'),
      () => import('../contexts/AuthContext'),
      () => import('../contexts/ThemeContext')
    ];

    try {
      const componentPromises = criticalComponents.map(async (importFn, index) => {
        const component = await importFn();
        this.componentCache.set(`component_${index}`, component);
        return component;
      });

      await Promise.allSettled(componentPromises);
      console.log('✅ Turbo: Critical components preloaded');
    } catch (error) {
      console.warn('⚠️ Turbo: Component preloading failed:', error);
    }
  }

  // Preload with Turbo-specific optimizations
  async initializeTurboPreloader(token?: string) {
    if (typeof window === 'undefined') return;

    console.log('🚀 Turbo: Initializing enhanced preloader...');

    // Run preloading strategies optimized for Turbo
    const preloadTasks = [
      this.preloadCriticalComponents(),
    ];

    if (token) {
      preloadTasks.push(
        this.preloadCriticalData(token),
        this.preloadDashboardData(token)
      );
    }

    await Promise.allSettled(preloadTasks);
    console.log('✅ Turbo: Enhanced preloader initialized');
  }

  // Setup interaction-based preloading for better performance
  setupInteractionPreloading(token?: string) {
    if (typeof window === 'undefined') return;

    const preloadOnInteraction = () => {
      this.initializeTurboPreloader(token);

      // Remove listeners after first interaction
      document.removeEventListener('mouseenter', preloadOnInteraction);
      document.removeEventListener('touchstart', preloadOnInteraction);
      document.removeEventListener('focus', preloadOnInteraction);
    };

    // Add interaction listeners with passive option for better performance
    document.addEventListener('mouseenter', preloadOnInteraction, { once: true, passive: true });
    document.addEventListener('touchstart', preloadOnInteraction, { once: true, passive: true });
    document.addEventListener('focus', preloadOnInteraction, { once: true, passive: true });
  }
}

// Enhanced preloader instance
export const turboPreloader = new TurboPreloader();
