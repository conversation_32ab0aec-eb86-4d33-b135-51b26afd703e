import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';

interface OptimizedProfileHook {
  profile: any;
  isLoading: boolean;
  error: string | null;
  refreshProfile: () => Promise<void>;
}

/**
 * Optimized profile hook that loads profile data efficiently
 * - Uses cached data first for immediate display
 * - Loads fresh data in background
 * - Minimizes loading states
 */
export function useOptimizedProfile(): OptimizedProfileHook {
  const { user, profile, loadProfile, isLoading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load profile data on mount if user exists but no profile
  useEffect(() => {
    if (user && !profile && !authLoading) {
      loadProfileData();
    }
  }, [user, profile, authLoading]);

  const loadProfileData = useCallback(async () => {
    if (isLoading) return; // Prevent duplicate calls
    
    try {
      setIsLoading(true);
      setError(null);
      await loadProfile();
    } catch (err: any) {
      setError(err.message || 'Failed to load profile');
      console.error('Profile loading error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [loadProfile, isLoading]);

  const refreshProfile = useCallback(async () => {
    await loadProfileData();
  }, [loadProfileData]);

  return {
    profile,
    isLoading: isLoading || authLoading,
    error,
    refreshProfile,
  };
}
