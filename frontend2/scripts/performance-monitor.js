#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Performance monitoring script for the VetCare Pro frontend
 * Tracks bundle sizes, build times, and performance metrics
 */

const PERFORMANCE_DIR = path.join(__dirname, '..', 'performance-reports');
const BUNDLE_ANALYZER_PORT = 8888;

// Ensure performance directory exists
if (!fs.existsSync(PERFORMANCE_DIR)) {
  fs.mkdirSync(PERFORMANCE_DIR, { recursive: true });
}

function getCurrentTimestamp() {
  return new Date().toISOString();
}

function getPerformanceReport() {
  const timestamp = getCurrentTimestamp();
  const reportFile = path.join(PERFORMANCE_DIR, `performance-${Date.now()}.json`);
  
  return {
    timestamp,
    reportFile,
    metrics: {
      buildTime: null,
      bundleSize: null,
      chunkSizes: {},
      webVitals: {},
      optimizations: []
    }
  };
}

function logPerformanceMetrics(metrics) {
  console.log('\n🚀 Performance Report');
  console.log('=====================');
  console.log(`Timestamp: ${metrics.timestamp}`);
  
  if (metrics.metrics.buildTime) {
    console.log(`Build Time: ${metrics.metrics.buildTime}ms`);
  }
  
  if (metrics.metrics.bundleSize) {
    console.log(`Bundle Size: ${metrics.metrics.bundleSize}`);
  }
  
  if (Object.keys(metrics.metrics.chunkSizes).length > 0) {
    console.log('\nChunk Sizes:');
    Object.entries(metrics.metrics.chunkSizes).forEach(([chunk, size]) => {
      console.log(`  ${chunk}: ${size}`);
    });
  }
  
  if (metrics.metrics.optimizations.length > 0) {
    console.log('\nOptimizations Applied:');
    metrics.metrics.optimizations.forEach(opt => {
      console.log(`  ✅ ${opt}`);
    });
  }
  
  console.log('=====================\n');
}

function savePerformanceReport(metrics) {
  try {
    fs.writeFileSync(metrics.reportFile, JSON.stringify(metrics, null, 2));
    console.log(`📊 Performance report saved: ${metrics.reportFile}`);
  } catch (error) {
    console.error('Failed to save performance report:', error);
  }
}

function analyzeBundle() {
  console.log('🔍 Analyzing bundle...');
  
  const { spawn } = require('child_process');
  
  return new Promise((resolve, reject) => {
    const analyzer = spawn('npm', ['run', 'build:analyze'], {
      stdio: 'inherit',
      shell: true
    });
    
    analyzer.on('close', (code) => {
      if (code === 0) {
        console.log(`📈 Bundle analyzer available at http://localhost:${BUNDLE_ANALYZER_PORT}`);
        resolve();
      } else {
        reject(new Error(`Bundle analysis failed with code ${code}`));
      }
    });
  });
}

function measureBuildTime() {
  console.log('⏱️  Measuring build time...');
  
  const { spawn } = require('child_process');
  const startTime = Date.now();
  
  return new Promise((resolve, reject) => {
    const build = spawn('npm', ['run', 'build'], {
      stdio: 'inherit',
      shell: true
    });
    
    build.on('close', (code) => {
      const buildTime = Date.now() - startTime;
      
      if (code === 0) {
        console.log(`✅ Build completed in ${buildTime}ms`);
        resolve(buildTime);
      } else {
        reject(new Error(`Build failed with code ${code}`));
      }
    });
  });
}

async function runPerformanceCheck() {
  const report = getPerformanceReport();
  
  try {
    // Measure build time
    const buildTime = await measureBuildTime();
    report.metrics.buildTime = buildTime;
    
    // Add optimization notes
    report.metrics.optimizations = [
      'Reduced framer-motion animations',
      'Optimized icon imports',
      'Simplified MedicalLoader component',
      'Enhanced Next.js configuration',
      'Improved AuthContext loading',
      'Added bundle splitting',
      'Enabled image optimization',
      'Implemented efficient caching'
    ];
    
    // Log and save report
    logPerformanceMetrics(report);
    savePerformanceReport(report);
    
    console.log('🎉 Performance check completed!');
    
  } catch (error) {
    console.error('❌ Performance check failed:', error);
    process.exit(1);
  }
}

// CLI interface
const command = process.argv[2];

switch (command) {
  case 'check':
    runPerformanceCheck();
    break;
    
  case 'analyze':
    analyzeBundle();
    break;
    
  case 'build-time':
    measureBuildTime().then(time => {
      console.log(`Build time: ${time}ms`);
    });
    break;
    
  default:
    console.log('Usage: node performance-monitor.js [check|analyze|build-time]');
    console.log('  check      - Run full performance check');
    console.log('  analyze    - Analyze bundle with webpack-bundle-analyzer');
    console.log('  build-time - Measure build time only');
    break;
}
