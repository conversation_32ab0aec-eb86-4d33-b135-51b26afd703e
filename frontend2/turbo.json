{"$schema": "https://turbo.build/schema.json", "pipeline": {"build": {"outputs": [".next/**", "!.next/cache/**"], "env": ["NODE_ENV", "NEXT_PUBLIC_*"], "cache": true}, "dev": {"cache": false, "persistent": true, "env": ["NODE_ENV", "NEXT_PUBLIC_*"]}, "dev:fast": {"cache": false, "persistent": true, "env": ["NODE_ENV", "NEXT_PUBLIC_*", "NEXT_TELEMETRY_DISABLED"]}, "start": {"dependsOn": ["build"], "cache": false, "persistent": true}, "lint": {"outputs": [], "cache": true}, "type-check": {"outputs": [], "cache": true}}, "globalDependencies": ["package.json", "next.config.ts", "tailwind.config.ts", "tsconfig.json", ".env*"]}