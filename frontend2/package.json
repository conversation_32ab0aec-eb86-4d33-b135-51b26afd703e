{"name": "frontend2", "version": "0.1.0", "private": true, "packageManager": "npm@10.0.0", "scripts": {"dev": "turbo dev", "dev:next": "next dev --turbo --port 3000", "dev:fast": "turbo dev:fast", "dev:legacy": "next dev", "dev:debug": "next dev --turbo --inspect", "dev:perf": "NEXT_TELEMETRY_DISABLED=1 next dev --turbo", "dev:preload": "next dev --turbo --port 3000 --experimental-app-dir", "build": "turbo build", "build:next": "next build", "build:turbo": "next build --turbo", "build:analyze": "ANALYZE=true next build", "build:debug": "NEXT_DEBUG=1 next build", "build:fast": "NEXT_TELEMETRY_DISABLED=1 next build", "start": "next start", "start:fast": "NEXT_TELEMETRY_DISABLED=1 next start", "start:prod": "NODE_ENV=production next start", "start:turbo": "next start --turbo", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "clean": "rm -rf .next out", "clean:all": "rm -rf .next out node_modules package-lock.json && npm install", "clean:turbo": "rm -rf .next/cache && npm run dev", "perf:build": "time npm run build", "perf:dev": "time next dev --turbo --port 3000", "perf:test": "node scripts/turbo-performance.js compare", "perf:baseline": "node scripts/turbo-performance.js baseline", "perf:monitor": "node scripts/turbo-performance.js build", "perf:check": "node scripts/performance-monitor.js check", "perf:analyze-bundle": "node scripts/performance-monitor.js analyze", "perf:build-time": "node scripts/performance-monitor.js build-time", "turbo:info": "next info --turbo"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.80.7", "axios": "^1.9.0", "clsx": "^2.1.1", "framer-motion": "^11.18.2", "lucide-react": "^0.514.0", "next": "15.3.3", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-icons": "^5.5.0", "tailwind-merge": "^2.5.4", "zod": "^3.25.63", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.4", "@svgr/webpack": "^8.1.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "critters": "^0.0.23", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.3", "next-compose-plugins": "^2.2.1", "postcss": "^8.5.0", "tailwindcss": "^3.4.17", "turbo": "^2.5.5", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.2"}}